<?= $this->extend('templates/system_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?= esc($duty_instruction['duty_instruction_title']) ?></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('duty-instructions') ?>">Duty Instructions</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><?= esc($duty_instruction['duty_instruction_number']) ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?= base_url('duty-instructions/' . $duty_instruction['id'] . '/edit') ?>" class="btn btn-primary me-2">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?= base_url('duty-instructions') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Duty Instruction Details -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Instruction Details</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Instruction Number:</strong></div>
                        <div class="col-sm-9">
                            <span class="badge bg-secondary"><?= esc($duty_instruction['duty_instruction_number']) ?></span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Title:</strong></div>
                        <div class="col-sm-9"><?= esc($duty_instruction['duty_instruction_title']) ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Workplan:</strong></div>
                        <div class="col-sm-9">
                            <?php if (!empty($duty_instruction['workplan_title'])): ?>
                                <span class="badge bg-info"><?= esc($duty_instruction['workplan_title']) ?></span>
                            <?php else: ?>
                                <span class="text-muted">No workplan assigned</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php if (!empty($duty_instruction['duty_instruction_description'])): ?>
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Description:</strong></div>
                            <div class="col-sm-9"><?= nl2br(esc($duty_instruction['duty_instruction_description'])) ?></div>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($duty_instruction['duty_instruction_filepath'])): ?>
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Attachment:</strong></div>
                            <div class="col-sm-9">
                                <a href="<?= base_url($duty_instruction['duty_instruction_filepath']) ?>" 
                                   class="btn btn-sm btn-outline-primary" target="_blank">
                                    <i class="fas fa-download"></i> Download File
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Status and Actions -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Status & Actions</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Current Status:</strong><br>
                        <?php
                        $statusClass = match($duty_instruction['status']) {
                            'pending' => 'bg-warning',
                            'approved' => 'bg-success',
                            'rejected' => 'bg-danger',
                            'completed' => 'bg-primary',
                            default => 'bg-secondary'
                        };
                        ?>
                        <span class="badge <?= $statusClass ?> fs-6"><?= esc(ucfirst($duty_instruction['status'])) ?></span>
                    </div>

                    <!-- Status Update Form -->
                    <?= form_open('duty-instructions/' . $duty_instruction['id'] . '/status') ?>
                        <?= csrf_field() ?>
                        <div class="mb-3">
                            <label for="status" class="form-label">Update Status:</label>
                            <select class="form-select form-select-sm" name="status" id="status">
                                <option value="pending" <?= $duty_instruction['status'] == 'pending' ? 'selected' : '' ?>>Pending</option>
                                <option value="approved" <?= $duty_instruction['status'] == 'approved' ? 'selected' : '' ?>>Approved</option>
                                <option value="rejected" <?= $duty_instruction['status'] == 'rejected' ? 'selected' : '' ?>>Rejected</option>
                                <option value="completed" <?= $duty_instruction['status'] == 'completed' ? 'selected' : '' ?>>Completed</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="status_remarks" class="form-label">Remarks:</label>
                            <textarea class="form-control form-control-sm" name="status_remarks" id="status_remarks" rows="2"></textarea>
                        </div>
                        <button type="submit" class="btn btn-sm btn-primary">Update Status</button>
                    <?= form_close() ?>

                    <hr>
                    <div class="text-muted small">
                        <div><strong>Created:</strong> <?= date('M d, Y H:i', strtotime($duty_instruction['created_at'])) ?></div>
                        <div><strong>By:</strong> <?= esc($duty_instruction['created_by_name'] ?? 'Unknown') ?></div>
                        <?php if (!empty($duty_instruction['updated_at'])): ?>
                            <div><strong>Updated:</strong> <?= date('M d, Y H:i', strtotime($duty_instruction['updated_at'])) ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Instruction Items -->
    <div class="card mt-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">Instruction Items</h6>
            <a href="<?= base_url('duty-instructions/' . $duty_instruction['id'] . '/items/new') ?>" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Add Item
            </a>
        </div>
        <div class="card-body">
            <?php if (!empty($duty_items)): ?>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Item #</th>
                                <th>Instruction</th>
                                <th>Status</th>
                                <th>Remarks</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($duty_items as $item): ?>
                                <tr>
                                    <td><span class="badge bg-light text-dark"><?= esc($item['instruction_number']) ?></span></td>
                                    <td><?= esc($item['instruction']) ?></td>
                                    <td>
                                        <?php
                                        $itemStatusClass = match($item['status']) {
                                            'active' => 'bg-success',
                                            'inactive' => 'bg-secondary',
                                            'completed' => 'bg-primary',
                                            default => 'bg-secondary'
                                        };
                                        ?>
                                        <span class="badge <?= $itemStatusClass ?>"><?= esc(ucfirst($item['status'])) ?></span>
                                    </td>
                                    <td><?= esc($item['remarks'] ?? '-') ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" title="Edit Item">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-list fa-2x mb-3"></i>
                    <br>No instruction items added yet.
                    <br><a href="<?= base_url('duty-instructions/' . $duty_instruction['id'] . '/items/new') ?>" class="btn btn-primary mt-2">
                        <i class="fas fa-plus"></i> Add First Item
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
